"use client";

import { useState } from "react";
import { User } from "@/utils/types";
import { useCart } from "@/contexts/cart-context";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, ArrowRight, Check } from "lucide-react";
import CheckoutStepShipping from "./checkout-step-shipping";
import CheckoutStepReview from "./checkout-step-review";
import CheckoutStepPayment from "./checkout-step-payment";
import CheckoutOrderSummary from "./checkout-order-summary";

interface CheckoutContentProps {
  user: User;
}

export interface CheckoutData {
  shipping: {
    fullName: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    district: string;
    postalCode: string;
    country: string;
    notes?: string;
  };
  discountCode?: string;
  discountAmount: number;
}

const STEPS = [
  { id: 1, title: "Shipping Information", description: "Enter your delivery details" },
  { id: 2, title: "Review Order", description: "Confirm your order details" },
  { id: 3, title: "Payment", description: "Complete your payment" },
];

export default function CheckoutContent({ user }: CheckoutContentProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [checkoutData, setCheckoutData] = useState<CheckoutData>({
    shipping: {
      fullName: user.name || "",
      email: user.email || "",
      phone: "",
      address: "",
      city: "",
      district: "Maseru",
      postalCode: "",
      country: "Lesotho",
      notes: "",
    },
    discountAmount: 0,
  });
  const [isProcessing, setIsProcessing] = useState(false);

  const { state: cartState } = useCart();

  const updateCheckoutData = (data: Partial<CheckoutData>) => {
    setCheckoutData(prev => ({ ...prev, ...data }));
  };

  const nextStep = () => {
    if (currentStep < STEPS.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = (currentStep / STEPS.length) * 100;

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Checkout</h1>
        
        {/* Progress Bar */}
        <div className="mb-6">
          <Progress value={progress} className="h-2 mb-4" />
          <div className="flex justify-between">
            {STEPS.map((step) => (
              <div
                key={step.id}
                className={`flex items-center ${
                  step.id <= currentStep ? "text-blue-600" : "text-gray-400"
                }`}
              >
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 mr-3 ${
                    step.id < currentStep
                      ? "bg-blue-600 border-blue-600 text-white"
                      : step.id === currentStep
                      ? "border-blue-600 text-blue-600"
                      : "border-gray-300 text-gray-400"
                  }`}
                >
                  {step.id < currentStep ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <span className="text-sm font-medium">{step.id}</span>
                  )}
                </div>
                <div className="hidden sm:block">
                  <div className="font-medium">{step.title}</div>
                  <div className="text-sm text-gray-500">{step.description}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>{STEPS[currentStep - 1].title}</CardTitle>
            </CardHeader>
            <CardContent>
              {currentStep === 1 && (
                <CheckoutStepShipping
                  data={checkoutData}
                  onUpdate={updateCheckoutData}
                  onNext={nextStep}
                />
              )}
              
              {currentStep === 2 && (
                <CheckoutStepReview
                  data={checkoutData}
                  onUpdate={updateCheckoutData}
                  onNext={nextStep}
                  onPrev={prevStep}
                />
              )}
              
              {currentStep === 3 && (
                <CheckoutStepPayment
                  data={checkoutData}
                  onPrev={prevStep}
                  isProcessing={isProcessing}
                  setIsProcessing={setIsProcessing}
                />
              )}
            </CardContent>
          </Card>
        </div>

        {/* Order Summary */}
        <div className="lg:col-span-1">
          <CheckoutOrderSummary
            checkoutData={checkoutData}
            onUpdate={updateCheckoutData}
            currentStep={currentStep}
          />
        </div>
      </div>
    </div>
  );
}
