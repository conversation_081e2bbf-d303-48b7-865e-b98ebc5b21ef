"use client";

import { useState } from "react";
import { CheckoutData } from "./checkout-content";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowRight, MessageCircle, AlertCircle } from "lucide-react";
import {
  validateLesothoPhoneNumber,
  validateCountryRestriction,
  LESOTHO_DISTRICTS,
} from "@/lib/product-utils";

interface CheckoutStepShippingProps {
  data: CheckoutData;
  onUpdate: (data: Partial<CheckoutData>) => void;
  onNext: () => void;
}

export default function CheckoutStepShipping({
  data,
  onUpdate,
  onNext,
}: CheckoutStepShippingProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!data.shipping.fullName.trim()) {
      newErrors.fullName = "Full name is required";
    }

    if (!data.shipping.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(data.shipping.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Validate WhatsApp number for Lesotho
    if (!data.shipping.phone.trim()) {
      newErrors.phone = "WhatsApp number is required";
    } else {
      const phoneValidation = validateLesothoPhoneNumber(data.shipping.phone);
      if (!phoneValidation.isValid) {
        newErrors.phone = phoneValidation.error || "Invalid WhatsApp number";
      }
    }

    if (!data.shipping.address.trim()) {
      newErrors.address = "Address is required";
    }

    if (!data.shipping.city.trim()) {
      newErrors.city = "City is required";
    }

    if (!data.shipping.district.trim()) {
      newErrors.district = "District is required";
    }

    if (!data.shipping.postalCode.trim()) {
      newErrors.postalCode = "Postal code is required";
    }

    // Validate country restriction
    if (!data.shipping.country.trim()) {
      newErrors.country = "Country is required";
    } else {
      const countryValidation = validateCountryRestriction(
        data.shipping.country
      );
      if (!countryValidation.isValid) {
        newErrors.country = countryValidation.error || "Invalid country";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onNext();
    }
  };

  const updateShipping = (field: string, value: string) => {
    // Auto-format phone number for Lesotho
    if (field === "phone") {
      const phoneValidation = validateLesothoPhoneNumber(value);
      if (phoneValidation.isValid && phoneValidation.formatted) {
        value = phoneValidation.formatted;
      }
    }

    onUpdate({
      shipping: {
        ...data.shipping,
        [field]: value,
      },
    });

    // Clear error for this field
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Geographic Restriction Notice */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          For customer support, contact us on WhatsApp: +266 6284 4473
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Full Name */}
        <div>
          <Label htmlFor="fullName">Full Name *</Label>
          <Input
            id="fullName"
            value={data.shipping.fullName}
            onChange={(e) => updateShipping("fullName", e.target.value)}
            className={errors.fullName ? "border-red-500" : ""}
            placeholder="Enter your full name"
          />
          {errors.fullName && (
            <p className="text-red-500 text-sm mt-1">{errors.fullName}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <Label htmlFor="email">Email Address *</Label>
          <Input
            id="email"
            type="email"
            value={data.shipping.email}
            onChange={(e) => updateShipping("email", e.target.value)}
            className={errors.email ? "border-red-500" : ""}
            placeholder="Enter your email"
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email}</p>
          )}
        </div>
      </div>

      {/* WhatsApp Number */}
      <div>
        <Label htmlFor="phone" className="flex items-center gap-2">
          <MessageCircle className="h-4 w-4" />
          WhatsApp Number *
        </Label>
        <Input
          id="phone"
          value={data.shipping.phone}
          onChange={(e) => updateShipping("phone", e.target.value)}
          className={errors.phone ? "border-red-500" : ""}
          placeholder="e.g., +266 5316 3354 or 53163354"
        />
        {errors.phone && (
          <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
        )}
        city
        <p className="text-sm text-gray-500 mt-1">
          Please provide your WhatsApp number for order updates and delivery
          coordination
        </p>
      </div>

      {/* Address */}
      <div>
        <Label htmlFor="address">Street Address *</Label>
        <Input
          id="address"
          value={data.shipping.address}
          onChange={(e) => updateShipping("address", e.target.value)}
          className={errors.address ? "border-red-500" : ""}
          placeholder="Enter your street address"
        />
        {errors.address && (
          <p className="text-red-500 text-sm mt-1">{errors.address}</p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* City */}
        <div>
          <Label htmlFor="city">Village *</Label>
          <Input
            id="city"
            value={data.shipping.city}
            onChange={(e) => updateShipping("city", e.target.value)}
            className={errors.city ? "border-red-500" : ""}
            placeholder="Enter your village"
          />
          {errors.city && (
            <p className="text-red-500 text-sm mt-1">{errors.city}</p>
          )}
        </div>

        {/* District */}
        <div>
          <Label htmlFor="district">District *</Label>
          <Select
            value={data.shipping.district}
            onValueChange={(value) => updateShipping("district", value)}
          >
            <SelectTrigger className={errors.district ? "border-red-500" : ""}>
              <SelectValue placeholder="Select district" />
            </SelectTrigger>
            <SelectContent>
              {LESOTHO_DISTRICTS.map((district) => (
                <SelectItem key={district} value={district}>
                  {district}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.district && (
            <p className="text-red-500 text-sm mt-1">{errors.district}</p>
          )}
          <p className="text-sm text-gray-500 mt-1">
            {data.shipping.district === "Maseru"
              ? "Free delivery in Maseru district"
              : "Free delivery for orders over M3500, otherwise delivery fee applies"}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Postal Code */}
        <div>
          <Label htmlFor="postalCode">Postal Code *</Label>
          <Input
            id="postalCode"
            value={data.shipping.postalCode}
            onChange={(e) => updateShipping("postalCode", e.target.value)}
            className={errors.postalCode ? "border-red-500" : ""}
            placeholder="Enter postal code"
          />
          {errors.postalCode && (
            <p className="text-red-500 text-sm mt-1">{errors.postalCode}</p>
          )}
        </div>

        {/* Country */}
        <div>
          <Label htmlFor="country">Country *</Label>
          <Select
            value={data.shipping.country}
            onValueChange={(value) => updateShipping("country", value)}
          >
            <SelectTrigger className={errors.country ? "border-red-500" : ""}>
              <SelectValue placeholder="Select country" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Lesotho">Lesotho</SelectItem>
            </SelectContent>
          </Select>
          {errors.country && (
            <p className="text-red-500 text-sm mt-1">{errors.country}</p>
          )}
        </div>
      </div>

      {/* Delivery Notes */}
      <div>
        <Label htmlFor="notes">Delivery Notes (Optional)</Label>
        <Textarea
          id="notes"
          value={data.shipping.notes || ""}
          onChange={(e) => updateShipping("notes", e.target.value)}
          placeholder="Any special delivery instructions..."
          rows={3}
        />
      </div>

      {/* Continue Button */}
      <div className="flex justify-end pt-4">
        <Button type="submit" size="lg">
          Continue to Review
          <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
      </div>
    </form>
  );
}
