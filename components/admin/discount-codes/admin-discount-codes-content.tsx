"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Tag,
  Percent,
  DollarSign,
  Calendar,
  Users,
  Eye,
  EyeOff,
  Copy,
  CheckCircle
} from "lucide-react";
import { useState, useEffect } from "react";
import { DiscountType } from "@/utils/types";
import { getDiscountCodes, createDiscountCode, updateDiscountCode, deleteDiscountCode } from "@/actions/discountCodeActions";

interface DiscountCodeData {
  id: string;
  code: string;
  type: DiscountType;
  value: number;
  isActive: boolean;
  usageLimit?: number;
  usageCount: number;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function AdminDiscountCodesContent() {
  const [discountCodes, setDiscountCodes] = useState<DiscountCodeData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCodes, setFilteredCodes] = useState<DiscountCodeData[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCode, setEditingCode] = useState<DiscountCodeData | null>(null);
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    code: "",
    type: "PERCENTAGE" as DiscountType,
    value: 0,
    isActive: true,
    usageLimit: "",
    expiresAt: ""
  });

  useEffect(() => {
    const loadDiscountCodes = async () => {
      try {
        const result = await getDiscountCodes({
          search: searchTerm || undefined
        });

        if (result.success && result.data) {
          const formattedCodes = result.data.map(code => ({
            id: code.id,
            code: code.code,
            type: code.type,
            value: code.value,
            isActive: code.isActive,
            usageLimit: code.usageLimit || undefined,
            usageCount: code._count.orders,
            expiresAt: code.expiresAt?.toISOString(),
            createdAt: code.createdAt.toISOString(),
            updatedAt: code.updatedAt.toISOString()
          }));
          setDiscountCodes(formattedCodes);
          setFilteredCodes(formattedCodes);
        } else {
          console.error("Error loading discount codes:", result.error);
        }
      } catch (error) {
        console.error("Error loading discount codes:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDiscountCodes();
  }, []);

  useEffect(() => {
    const filtered = discountCodes.filter(code =>
      code.code.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredCodes(filtered);
  }, [searchTerm, discountCodes]);

  const handleOpenDialog = (code?: DiscountCodeData) => {
    if (code) {
      setEditingCode(code);
      setFormData({
        code: code.code,
        type: code.type,
        value: code.value,
        isActive: code.isActive,
        usageLimit: code.usageLimit?.toString() || "",
        expiresAt: code.expiresAt ? code.expiresAt.split('T')[0] : ""
      });
    } else {
      setEditingCode(null);
      setFormData({
        code: "",
        type: "PERCENTAGE",
        value: 0,
        isActive: true,
        usageLimit: "",
        expiresAt: ""
      });
    }
    setIsDialogOpen(true);
  };

  const handleSaveCode = async () => {
    try {
      const codeData = {
        ...formData,
        usageLimit: formData.usageLimit ? parseInt(formData.usageLimit) : undefined,
        expiresAt: formData.expiresAt ? new Date(`${formData.expiresAt}T23:59:59Z`) : undefined
      };

      if (editingCode) {
        // Update existing code
        const result = await updateDiscountCode(editingCode.id, codeData);
        if (result.success && result.data) {
          const updatedCodes = discountCodes.map(code =>
            code.id === editingCode.id
              ? {
                  ...code,
                  ...formData,
                  usageLimit: result.data.usageLimit || undefined,
                  expiresAt: result.data.expiresAt?.toISOString(),
                  updatedAt: result.data.updatedAt.toISOString()
                }
              : code
          );
          setDiscountCodes(updatedCodes);
          alert("Discount code updated successfully!");
        } else {
          alert(result.error || "Failed to update discount code");
        }
      } else {
        // Create new code
        const result = await createDiscountCode(codeData);
        if (result.success && result.data) {
          const newCode: DiscountCodeData = {
            id: result.data.id,
            code: result.data.code,
            type: result.data.type,
            value: result.data.value,
            isActive: result.data.isActive,
            usageLimit: result.data.usageLimit || undefined,
            usageCount: result.data.usageCount,
            expiresAt: result.data.expiresAt?.toISOString(),
            createdAt: result.data.createdAt.toISOString(),
            updatedAt: result.data.updatedAt.toISOString()
          };
          setDiscountCodes([...discountCodes, newCode]);
          alert("Discount code created successfully!");
        } else {
          alert(result.error || "Failed to create discount code");
        }
      }

      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error saving discount code:", error);
      alert("Failed to save discount code");
    }
  };

  const handleDeleteCode = async (codeId: string) => {
    if (!confirm("Are you sure you want to delete this discount code?")) return;

    try {
      const result = await deleteDiscountCode(codeId);

      if (result.success) {
        if (result.data) {
          // Code was marked as inactive instead of deleted
          const updatedCodes = discountCodes.map(code =>
            code.id === codeId ? { ...code, isActive: false } : code
          );
          setDiscountCodes(updatedCodes);
        } else {
          // Code was actually deleted
          const updatedCodes = discountCodes.filter(code => code.id !== codeId);
          setDiscountCodes(updatedCodes);
        }
        alert(result.message || "Discount code deleted successfully!");
      } else {
        alert(result.error || "Failed to delete discount code");
      }
    } catch (error) {
      console.error("Error deleting discount code:", error);
      alert("Failed to delete discount code");
    }
  };

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      setCopiedCode(code);
      setTimeout(() => setCopiedCode(null), 2000);
    } catch (error) {
      console.error("Failed to copy code:", error);
    }
  };

  const getStatusBadge = (isActive: boolean, expiresAt?: string) => {
    const isExpired = expiresAt && new Date(expiresAt) < new Date();
    
    if (isExpired) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    
    return isActive ? 
      <Badge variant="default" className="flex items-center gap-1">
        <Eye className="h-3 w-3" />
        Active
      </Badge> : 
      <Badge variant="secondary" className="flex items-center gap-1">
        <EyeOff className="h-3 w-3" />
        Inactive
      </Badge>;
  };

  const getDiscountDisplay = (type: DiscountType, value: number) => {
    return type === "PERCENTAGE" ? `${value}%` : `M${value}`;
  };

  const getUsageDisplay = (usageCount: number, usageLimit?: number) => {
    if (usageLimit) {
      const percentage = (usageCount / usageLimit) * 100;
      return `${usageCount}/${usageLimit} (${percentage.toFixed(0)}%)`;
    }
    return `${usageCount} uses`;
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Discount Codes</h1>
          <p className="text-gray-600 mt-2">Manage promotional discount codes and offers</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => handleOpenDialog()}>
              <Plus className="h-4 w-4 mr-2" />
              Add Discount Code
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingCode ? "Edit Discount Code" : "Create New Discount Code"}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="code">Discount Code</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value.toUpperCase() })}
                  placeholder="Enter discount code (e.g., SAVE20)"
                />
              </div>
              <div>
                <Label htmlFor="type">Discount Type</Label>
                <Select value={formData.type} onValueChange={(value) => setFormData({ ...formData, type: value as DiscountType })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PERCENTAGE">Percentage</SelectItem>
                    <SelectItem value="FIXED_AMOUNT">Fixed Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="value">
                  {formData.type === "PERCENTAGE" ? "Percentage (%)" : "Amount (M)"}
                </Label>
                <Input
                  id="value"
                  type="number"
                  value={formData.value}
                  onChange={(e) => setFormData({ ...formData, value: parseFloat(e.target.value) || 0 })}
                  placeholder={formData.type === "PERCENTAGE" ? "10" : "50"}
                />
              </div>
              <div>
                <Label htmlFor="usageLimit">Usage Limit (Optional)</Label>
                <Input
                  id="usageLimit"
                  type="number"
                  value={formData.usageLimit}
                  onChange={(e) => setFormData({ ...formData, usageLimit: e.target.value })}
                  placeholder="Leave empty for unlimited"
                />
              </div>
              <div>
                <Label htmlFor="expiresAt">Expiration Date (Optional)</Label>
                <Input
                  id="expiresAt"
                  type="date"
                  value={formData.expiresAt}
                  onChange={(e) => setFormData({ ...formData, expiresAt: e.target.value })}
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                />
                <Label htmlFor="isActive">Active Code</Label>
              </div>
              <div className="flex gap-2 pt-4">
                <Button onClick={handleSaveCode} className="flex-1">
                  {editingCode ? "Update Code" : "Create Code"}
                </Button>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search discount codes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Tag className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Codes</p>
                <p className="text-2xl font-bold">{discountCodes.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Active Codes</p>
                <p className="text-2xl font-bold">{discountCodes.filter(c => c.isActive).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Total Uses</p>
                <p className="text-2xl font-bold">{discountCodes.reduce((sum, c) => sum + c.usageCount, 0)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Avg. Discount</p>
                <p className="text-2xl font-bold">
                  {discountCodes.filter(c => c.type === "PERCENTAGE").length > 0 
                    ? `${(discountCodes.filter(c => c.type === "PERCENTAGE").reduce((sum, c) => sum + c.value, 0) / discountCodes.filter(c => c.type === "PERCENTAGE").length).toFixed(0)}%`
                    : "N/A"
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Discount Codes List */}
      <Card>
        <CardHeader>
          <CardTitle>Discount Codes ({filteredCodes.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredCodes.length === 0 ? (
              <div className="text-center py-8">
                <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No discount codes found</p>
                {searchTerm && (
                  <p className="text-sm text-gray-500 mt-2">
                    Try adjusting your search terms
                  </p>
                )}
              </div>
            ) : (
              filteredCodes.map((code) => (
                <div key={code.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      {code.type === "PERCENTAGE" ? (
                        <Percent className="h-8 w-8 text-white" />
                      ) : (
                        <DollarSign className="h-8 w-8 text-white" />
                      )}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold text-lg font-mono">{code.code}</h3>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyCode(code.code)}
                          className="h-6 w-6 p-0"
                        >
                          {copiedCode === code.code ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <Copy className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      <p className="text-gray-600 text-sm">
                        {getDiscountDisplay(code.type, code.value)} discount
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusBadge(code.isActive, code.expiresAt)}
                        <Badge variant="outline">
                          <Users className="h-3 w-3 mr-1" />
                          {getUsageDisplay(code.usageCount, code.usageLimit)}
                        </Badge>
                        {code.expiresAt && (
                          <Badge variant="outline">
                            <Calendar className="h-3 w-3 mr-1" />
                            Expires {new Date(code.expiresAt).toLocaleDateString()}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right text-sm text-gray-600">
                      <p>Created: {new Date(code.createdAt).toLocaleDateString()}</p>
                      <p>Updated: {new Date(code.updatedAt).toLocaleDateString()}</p>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleOpenDialog(code)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteCode(code.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
