"use client";

import { useState, useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Upload, 
  FileImage, 
  X, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Eye,
  Trash2
} from "lucide-react";
import { UploadedImage } from "./bulk-upload-content";
import { UploadDropzone } from "@/lib/uploadthing";

interface BulkImageUploadProps {
  onImagesUploaded: (images: UploadedImage[]) => void;
  uploadedImages: UploadedImage[];
}

interface UploadProgress {
  [key: string]: number;
}

export default function BulkImageUpload({ onImagesUploaded, uploadedImages }: BulkImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress>({});
  const [uploadErrors, setUploadErrors] = useState<string[]>([]);
  const [selectedImages, setSelectedImages] = useState<Set<string>>(new Set());

  const handleUploadComplete = (res: any[]) => {
    if (res && res.length > 0) {
      const newImages: UploadedImage[] = res.map(file => ({
        id: `${Date.now()}-${Math.random()}`,
        url: file.url,
        name: file.name,
        size: file.size,
        assigned: false,
      }));

      const allImages = [...uploadedImages, ...newImages];
      onImagesUploaded(allImages);
      setUploadProgress({});
      setUploadErrors([]);
    }
    setIsUploading(false);
  };

  const handleUploadError = (error: Error) => {
    console.error("Upload error:", error);
    setUploadErrors(prev => [...prev, error.message]);
    setIsUploading(false);
    setUploadProgress({});
  };

  const removeImage = (imageId: string) => {
    const updatedImages = uploadedImages.filter(img => img.id !== imageId);
    onImagesUploaded(updatedImages);
    setSelectedImages(prev => {
      const newSet = new Set(prev);
      newSet.delete(imageId);
      return newSet;
    });
  };

  const toggleImageSelection = (imageId: string) => {
    setSelectedImages(prev => {
      const newSet = new Set(prev);
      if (newSet.has(imageId)) {
        newSet.delete(imageId);
      } else {
        newSet.add(imageId);
      }
      return newSet;
    });
  };

  const removeSelectedImages = () => {
    const updatedImages = uploadedImages.filter(img => !selectedImages.has(img.id));
    onImagesUploaded(updatedImages);
    setSelectedImages(new Set());
  };

  const selectAllImages = () => {
    if (selectedImages.size === uploadedImages.length) {
      setSelectedImages(new Set());
    } else {
      setSelectedImages(new Set(uploadedImages.map(img => img.id)));
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      {/* Upload Instructions */}
      <div className="text-center">
        <FileImage className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Upload Product Images</h2>
        <p className="text-gray-600 mb-4">
          Upload multiple product images at once. Supported formats: JPEG, PNG, WebP (max 4MB each)
        </p>
        <div className="flex justify-center space-x-4 text-sm text-gray-500">
          <span>• Max 100 images per upload</span>
          <span>• 4MB per image</span>
          <span>• JPEG, PNG, WebP formats</span>
        </div>
      </div>

      {/* Upload Dropzone */}
      <Card className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
        <CardContent className="p-8">
          <UploadDropzone
            endpoint="bulkProductImageUploader"
            onClientUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            onUploadBegin={() => setIsUploading(true)}
            appearance={{
              container: "w-full border-none",
              uploadIcon: "text-gray-400",
              label: "text-gray-600 text-lg",
              allowedContent: "text-gray-500",
              button: "bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors hover:cursor-pointer"
            }}
            content={{
              label: "Drop images here or click to browse",
              allowedContent: "Images up to 4MB each, max 100 files",
              // button: "Choose Images"
            }}
          />
        </CardContent>
      </Card>

      {/* Upload Progress */}
      {isUploading && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">Uploading images...</span>
                </div>
                <Progress value={50} className="w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Errors */}
      {uploadErrors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {uploadErrors.map((error, index) => (
                <div key={index}>{error}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Uploaded Images */}
      {uploadedImages.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h3 className="text-lg font-semibold text-gray-900">
                Uploaded Images ({uploadedImages.length})
              </h3>
              <Badge variant="outline">
                {selectedImages.size} selected
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={selectAllImages}
              >
                {selectedImages.size === uploadedImages.length ? 'Deselect All' : 'Select All'}
              </Button>
              {selectedImages.size > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={removeSelectedImages}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  Remove Selected
                </Button>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {uploadedImages.map((image) => (
              <Card 
                key={image.id} 
                className={`relative group cursor-pointer transition-all ${
                  selectedImages.has(image.id) 
                    ? 'ring-2 ring-blue-500 bg-blue-50' 
                    : 'hover:shadow-md'
                }`}
                onClick={() => toggleImageSelection(image.id)}
              >
                <CardContent className="p-2">
                  <div className="aspect-square relative overflow-hidden rounded-lg bg-gray-100">
                    <img
                      src={image.url}
                      alt={image.name}
                      className="w-full h-full object-cover"
                    />
                    
                    {/* Selection Overlay */}
                    <div className={`absolute inset-0 flex items-center justify-center transition-opacity ${
                      selectedImages.has(image.id) 
                        ? 'bg-blue-500 bg-opacity-20 opacity-100' 
                        : 'bg-black bg-opacity-0 opacity-0 group-hover:opacity-100'
                    }`}>
                      {selectedImages.has(image.id) && (
                        <CheckCircle className="w-8 h-8 text-blue-600 bg-white rounded-full" />
                      )}
                    </div>

                    {/* Remove Button */}
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-1 right-1 w-6 h-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        removeImage(image.id);
                      }}
                    >
                      <X className="w-3 h-3" />
                    </Button>

                    {/* Assignment Status */}
                    {image.assigned && (
                      <Badge 
                        variant="secondary" 
                        className="absolute bottom-1 left-1 text-xs"
                      >
                        Assigned
                      </Badge>
                    )}
                  </div>
                  
                  <div className="mt-2 space-y-1">
                    <p className="text-xs font-medium text-gray-900 truncate">
                      {image.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(image.size)}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Continue Button */}
          <div className="flex justify-end pt-4">
            <Button
              onClick={() => onImagesUploaded(uploadedImages)}
              disabled={uploadedImages.length === 0}
              className="px-8"
            >
              Continue to Product Details
              <span className="ml-2">({uploadedImages.length} images)</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
