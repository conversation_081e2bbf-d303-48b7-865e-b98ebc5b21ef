"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Upload,
  FileImage,
  Package,
  CheckCircle,
  AlertCircle,
  Info,
  Download,
} from "lucide-react";
import { User } from "@/utils/types";
import BulkImageUpload from "./bulk-image-upload";
import ProductMetadataEntry from "./product-metadata-entry";
import ProductPreview from "./product-preview";

export interface UploadedImage {
  id: string;
  url: string;
  name: string;
  size: number;
  assigned?: boolean;
}

export interface ProductData {
  id: string;
  name: string;
  description: string;
  price: number;
  discountedPrice?: number;
  brand: string;
  categoryId: string;
  images: string[];
  sizes: string[];
  stock: number;
  isActive: boolean;
}

export interface BulkUploadState {
  step: "images" | "metadata" | "preview" | "complete";
  uploadedImages: UploadedImage[];
  products: ProductData[];
  isProcessing: boolean;
  progress: number;
  errors: string[];
  successCount: number;
}

type Props = {
  user: User;
};

export default function BulkProductUploadContent({ user }: Props) {
  const [state, setState] = useState<BulkUploadState>({
    step: "images",
    uploadedImages: [],
    products: [],
    isProcessing: false,
    progress: 0,
    errors: [],
    successCount: 0,
  });
  const [categories, setCategories] = useState<
    Array<{ id: string; name: string }>
  >([{ id: "123", name: "Shoes" }]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  const [userRole, setUserRole] = useState<string | null>(null);
  const router = useRouter();

  // Check user role on client side
  useEffect(() => {
    setUserRole(user.role);
  }, [router]);

  // Load categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/admin/categories");
        const result = await response.json();
        if (result.success) {
          setCategories(result.data);
        }
      } catch (error) {
        console.error("Failed to fetch categories:", error);
      } finally {
        setLoadingCategories(false);
      }
    };

    fetchCategories();
  }, []);

  // Don't render content until role is verified
  if (userRole !== "ADMIN") {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Verifying access...</p>
        </div>
      </div>
    );
  }

  const updateState = (updates: Partial<BulkUploadState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  };

  const handleImagesUploaded = (images: UploadedImage[]) => {
    updateState({
      uploadedImages: images,
      step: images.length > 0 ? "metadata" : "images",
    });
  };

  const handleProductsCreated = (products: ProductData[]) => {
    updateState({
      products,
      step: products.length > 0 ? "preview" : "metadata",
    });
  };

  const handleSubmitProducts = async () => {
    updateState({ isProcessing: true, progress: 0, errors: [] });

    try {
      const response = await fetch("/api/admin/products/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ products: state.products }),
      });

      const result = await response.json();

      if (result.success) {
        updateState({
          step: "complete",
          successCount: result.data.summary.successful,
          errors: result.data.errors,
          progress: 100,
        });
      } else {
        updateState({
          errors: result.details || [result.error],
          progress: 0,
        });
      }
    } catch (error) {
      updateState({
        errors: ["Failed to submit products. Please try again."],
        progress: 0,
      });
    } finally {
      updateState({ isProcessing: false });
    }
  };

  const downloadTemplate = async () => {
    try {
      const response = await fetch("/api/admin/products/bulk/template");
      const result = await response.json();

      if (result.success) {
        const blob = new Blob([JSON.stringify(result.data, null, 2)], {
          type: "application/json",
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "bulk-upload-template.json";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Failed to download template:", error);
    }
  };

  const getStepStatus = (step: string) => {
    const currentStepIndex = [
      "images",
      "metadata",
      "preview",
      "complete",
    ].indexOf(state.step);
    const stepIndex = ["images", "metadata", "preview", "complete"].indexOf(
      step
    );

    if (stepIndex < currentStepIndex) return "complete";
    if (stepIndex === currentStepIndex) return "current";
    return "pending";
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center space-x-4 mb-8">
      {[
        { key: "images", label: "Upload Images", icon: FileImage },
        { key: "metadata", label: "Product Details", icon: Package },
        { key: "preview", label: "Review & Submit", icon: CheckCircle },
      ].map(({ key, label, icon: Icon }, index) => {
        const status = getStepStatus(key);
        return (
          <div key={key} className="flex items-center">
            <div
              className={`
              flex items-center justify-center w-10 h-10 rounded-full border-2
              ${
                status === "complete"
                  ? "bg-green-500 border-green-500 text-white"
                  : status === "current"
                  ? "bg-blue-500 border-blue-500 text-white"
                  : "bg-gray-200 border-gray-300 text-gray-500"
              }
            `}
            >
              <Icon className="w-5 h-5" />
            </div>
            <span
              className={`ml-2 text-sm font-medium ${
                status === "current"
                  ? "text-blue-600"
                  : status === "complete"
                  ? "text-green-600"
                  : "text-gray-500"
              }`}
            >
              {label}
            </span>
            {index < 2 && (
              <div
                className={`w-8 h-0.5 mx-4 ${
                  status === "complete" ? "bg-green-500" : "bg-gray-300"
                }`}
              />
            )}
          </div>
        );
      })}
    </div>
  );

  return (
    <div className="max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.push("/admin/products")}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Products</span>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Bulk Product Upload
            </h1>
            <p className="text-gray-600 mt-1">
              Upload multiple products efficiently
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={downloadTemplate}>
            <Download className="w-4 h-4 mr-2" />
            Download Template
          </Button>
          <Badge variant="outline" className="text-sm">
            {state.uploadedImages.length} images • {state.products.length}{" "}
            products
          </Badge>
        </div>
      </div>

      {/* Step Indicator */}
      {renderStepIndicator()}

      {/* Progress Bar */}
      {state.isProcessing && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Processing products...
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(state.progress)}%
            </span>
          </div>
          <Progress value={state.progress} className="w-full" />
        </div>
      )}

      {/* Error Display */}
      {state.errors.length > 0 && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {state.errors.slice(0, 5).map((error, index) => (
                <div key={index}>{error}</div>
              ))}
              {state.errors.length > 5 && (
                <div className="text-sm text-gray-600">
                  ... and {state.errors.length - 5} more errors
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Card>
        <CardContent className="p-6">
          {state.step === "images" && (
            <BulkImageUpload
              onImagesUploaded={handleImagesUploaded}
              uploadedImages={state.uploadedImages}
            />
          )}

          {state.step === "metadata" && (
            <ProductMetadataEntry
              uploadedImages={state.uploadedImages}
              categories={categories}
              loadingCategories={loadingCategories}
              onProductsCreated={handleProductsCreated}
              onBack={() => updateState({ step: "images" })}
            />
          )}

          {state.step === "preview" && (
            <ProductPreview
              products={state.products}
              uploadedImages={state.uploadedImages}
              onSubmit={handleSubmitProducts}
              onBack={() => updateState({ step: "metadata" })}
              isProcessing={state.isProcessing}
            />
          )}

          {state.step === "complete" && (
            <div className="text-center py-12">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Upload Complete!
              </h2>
              <p className="text-gray-600 mb-6">
                Successfully created {state.successCount} products
                {state.errors.length > 0 &&
                  ` with ${state.errors.length} errors`}
              </p>
              <div className="flex justify-center space-x-4">
                <Button onClick={() => router.push("/admin/products")}>
                  View Products
                </Button>
                <Button
                  variant="outline"
                  onClick={() =>
                    setState({
                      step: "images",
                      uploadedImages: [],
                      products: [],
                      isProcessing: false,
                      progress: 0,
                      errors: [],
                      successCount: 0,
                    })
                  }
                >
                  Upload More
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
