"use client";

import { useCart } from "@/contexts/cart-context";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { formatPrice } from "@/lib/product-utils";
import { ShoppingCart } from "lucide-react";
import Link from "next/link";

interface CartSummaryProps {
  showCheckoutButton?: boolean;
  className?: string;
}

export default function CartSummary({ showCheckoutButton = true, className = "" }: CartSummaryProps) {
  const { state: cartState } = useCart();

  if (cartState.items.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <ShoppingCart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-gray-500 mb-4">Your cart is empty</p>
          <Link href="/products">
            <Button variant="outline" size="sm">
              Start Shopping
            </Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  const subtotal = cartState.totalPrice;
  const shippingCost = subtotal >= 500 ? 0 : 50;
  const total = subtotal + shippingCost;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="h-5 w-5" />
          Cart Summary
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Items Summary */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Items ({cartState.totalItems})</span>
            <span>{formatPrice(subtotal)}</span>
          </div>
          
          <div className="flex justify-between text-sm">
            <span>Shipping</span>
            <span className={shippingCost === 0 ? "text-green-600" : ""}>
              {shippingCost === 0 ? "Free" : formatPrice(shippingCost)}
            </span>
          </div>
          
          {subtotal < 500 && (
            <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded">
              Add {formatPrice(500 - subtotal)} more for free shipping
            </div>
          )}
        </div>

        <Separator />

        {/* Total */}
        <div className="flex justify-between font-semibold text-lg">
          <span>Total</span>
          <span>{formatPrice(total)}</span>
        </div>

        {/* Action Buttons */}
        {showCheckoutButton && (
          <div className="space-y-2">
            <Link href="/checkout" className="w-full">
              <Button size="lg" className="w-full">
                Proceed to Checkout
              </Button>
            </Link>
            
            <Link href="/cart" className="w-full">
              <Button variant="outline" size="sm" className="w-full">
                View Cart
              </Button>
            </Link>
          </div>
        )}

        {/* Security Features */}
        <div className="pt-4 border-t">
          <div className="text-xs text-gray-500 space-y-1">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>Secure checkout</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
