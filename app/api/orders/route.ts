import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import { ApiResponse } from "@/utils/types";
import { getCurrentUser } from "@/lib/auth-utils";
import { sendOrderConfirmationEmail, sendAdminOrderNotification } from "@/lib/email-service";

// Generate unique order number
function generateOrderNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `ORD-${timestamp.slice(-6)}${random}`;
}

// GET /api/orders - Get user's orders
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");

    const where: any = { userId: user.id };
    if (status) {
      where.status = status;
    }

    const skip = (page - 1) * limit;
    const total = await prisma.order.count({ where });

    const orders = await prisma.order.findMany({
      where,
      include: {
        orderItems: {
          include: {
            product: {
              include: {
                category: true,
              },
            },
          },
        },
        discountCode: true,
        paymentProof: true,
      },
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    });

    const response: ApiResponse = {
      success: true,
      data: {
        data: orders,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching orders:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch orders" },
      { status: 500 }
    );
  }
}

// POST /api/orders - Create new order
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      shippingAddress,
      phoneNumber,
      notes,
      discountAmount = 0,
      discountCode,
      paymentMethod,
      paymentProofUrl,
      deliveryFee = 0,
      items,
    } = body;

    // Validate required fields
    if (!shippingAddress || !phoneNumber || !items || items.length === 0) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Calculate total amount
    let totalAmount = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId, isActive: true },
      });

      if (!product) {
        return NextResponse.json(
          { success: false, error: `Product ${item.productId} not found` },
          { status: 400 }
        );
      }

      if (product.stock < item.quantity) {
        return NextResponse.json(
          { success: false, error: `Insufficient stock for ${product.name}` },
          { status: 400 }
        );
      }

      const itemTotal = item.price * item.quantity;
      totalAmount += itemTotal;

      orderItems.push({
        productId: item.productId,
        quantity: item.quantity,
        price: item.price,
        size: item.size,
        color: item.color,
      });
    }

    // Use the delivery fee from the frontend calculation
    totalAmount = totalAmount - discountAmount + deliveryFee;

    // Validate discount code if provided
    let discountCodeId = null;
    if (discountCode) {
      const discount = await prisma.discountCode.findUnique({
        where: { code: discountCode, isActive: true },
      });

      if (!discount) {
        return NextResponse.json(
          { success: false, error: "Invalid discount code" },
          { status: 400 }
        );
      }

      // Check if discount is still valid
      if (discount.validUntil && discount.validUntil < new Date()) {
        return NextResponse.json(
          { success: false, error: "Discount code has expired" },
          { status: 400 }
        );
      }

      // Check usage limits
      if (discount.maxUses && discount.usedCount >= discount.maxUses) {
        return NextResponse.json(
          { success: false, error: "Discount code usage limit reached" },
          { status: 400 }
        );
      }

      discountCodeId = discount.id;
    }

    // Create order in transaction with increased timeout
    const result = await prisma.$transaction(async (tx) => {
      // Generate unique order number
      const orderNumber = generateOrderNumber();

      // Clear user's cart first (fastest operation)
      await tx.cartItem.deleteMany({
        where: { userId: user.id },
      });

      // Update product stock in batch
      const stockUpdates = items.map(item =>
        tx.product.update({
          where: { id: item.productId },
          data: {
            stock: {
              decrement: item.quantity,
            },
          },
        })
      );
      await Promise.all(stockUpdates);

      // Update discount code usage if applicable
      if (discountCodeId) {
        await tx.discountCode.update({
          where: { id: discountCodeId },
          data: {
            usedCount: {
              increment: 1,
            },
          },
        });
      }

      // Create order with items
      const order = await tx.order.create({
        data: {
          userId: user.id,
          orderNumber,
          status: "PENDING",
          totalAmount,
          discountAmount,
          discountCodeId,
          shippingAddress,
          phoneNumber,
          notes,
          orderItems: {
            create: orderItems,
          },
        },
        include: {
          orderItems: {
            include: {
              product: true,
            },
          },
          discountCode: true,
        },
      });

      // Create payment proof if provided
      if (paymentProofUrl) {
        await tx.paymentProof.create({
          data: {
            orderId: order.id,
            imageUrl: paymentProofUrl,
            status: "PENDING",
            notes: paymentMethod ? `Payment Method: ${paymentMethod}` : undefined,
          },
        });
      }

      return order;
    }, {
      maxWait: 10000, // 10 seconds max wait
      timeout: 15000, // 15 seconds timeout
    });

    // Send emails asynchronously (don't block the response)
    Promise.all([
      // Send customer confirmation email
      sendOrderConfirmationEmail(result).catch(error => {
        console.error("Failed to send order confirmation email:", error);
      }),
      // Send admin notification email
      sendAdminOrderNotification(result, paymentMethod).catch(error => {
        console.error("Failed to send admin order notification:", error);
      })
    ]).catch(error => {
      console.error("Error sending order emails:", error);
      // Don't fail the request if emails fail
    });

    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
      message: "Order created successfully",
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    console.error("Error creating order:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create order" },
      { status: 500 }
    );
  }
}
