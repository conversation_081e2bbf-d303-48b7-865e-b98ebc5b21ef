import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/lib/generated/prisma";
import { ApiResponse } from "@/utils/types";
import { requireAdmin } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// GET /api/admin/products/[id] - Get single product for admin
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin access
    await requireAdmin();
    const {id: paramsId} = await params;

    const product = await prisma.product.findUnique({
      where: { id: paramsId },
      include: {
        category: true,
        reviews: {
          orderBy: { createdAt: "desc" },
        },
        _count: {
          select: { 
            reviews: true,
            orderItems: true,
            cartItems: true
          }
        }
      },
    });

    if (!product) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    const response: ApiResponse<typeof product> = {
      success: true,
      data: product,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching admin product:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch product" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/products/[id] - Update product
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const {id: paramsId} = await params;
    // Check admin access
    await requireAdmin();

    const body = await request.json();
    const {
      name,
      description,
      price,
      discountedPrice,
      brand,
      categoryId,
      images,
      sizes,
      colors,
      stock,
      isActive,
    } = body;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: paramsId },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    // Verify category exists if categoryId is being updated
    if (categoryId && categoryId !== existingProduct.categoryId) {
      const category = await prisma.category.findUnique({
        where: { id: categoryId },
      });

      if (!category) {
        return NextResponse.json(
          { success: false, error: "Category not found" },
          { status: 400 }
        );
      }
    }

    const product = await prisma.product.update({
      where: { id: paramsId },
      data: {
        ...(name && { name }),
        ...(description !== undefined && { description }),
        ...(price && { price: parseFloat(price) }),
        ...(discountedPrice !== undefined && { 
          discountedPrice: discountedPrice ? parseFloat(discountedPrice) : null 
        }),
        ...(brand && { brand }),
        ...(categoryId && { categoryId }),
        ...(images && { images }),
        ...(sizes && { sizes }),
        ...(colors && { colors }),
        ...(stock !== undefined && { stock: parseInt(stock) }),
        ...(isActive !== undefined && { isActive }),
      },
      include: {
        category: true,
      },
    });

    const response: ApiResponse<typeof product> = {
      success: true,
      data: product,
      message: "Product updated successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error updating product:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update product" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/products/[id] - Delete product (soft delete)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin access
    await requireAdmin();
    const {id: paramsId} = await params;

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: paramsId },
    });

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: "Product not found" },
        { status: 404 }
      );
    }

    // Soft delete by setting isActive to false
    await prisma.product.update({
      where: { id: paramsId },
      data: { isActive: false },
    });

    const response: ApiResponse = {
      success: true,
      message: "Product deleted successfully",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error deleting product:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete product" },
      { status: 500 }
    );
  }
}
