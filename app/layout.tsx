import type { <PERSON>ada<PERSON> } from "next";
import { DM_Sans } from "next/font/google";
import "./globals.css";
import { CartProvider } from "@/contexts/cart-context";

const font = DM_Sans({
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Rivv Sneakers | Purposefully Curated. Unapologetically Premium.",
  description: "Purposefully Curated. Unapologetically Premium. RIVV is a proudly female-founded brand on a mission to deliver quality that speaks for itself. Every pair is carefully selected for its craftsmanship, comfort, and standout style. We're not here to meet expectations—we're here to exceed them. Step in with confidence. You won't be disappointed.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={font.className}
      >
        <CartProvider>
          {children}
        </CartProvider>
      </body>
    </html>
  );
}
