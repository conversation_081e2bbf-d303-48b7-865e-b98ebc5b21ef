"use server";

import prisma from "@/lib/prisma";
import { Product } from "@/utils/types";

/**
 * Get all products with optional filtering
 */
export async function getProducts(filters?: {
  search?: string;
  categoryId?: string;
  isActive?: boolean;
  isFeatured?: boolean;
}) {
  try {
    const where: any = {};

    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search, mode: "insensitive" } },
        { brand: { contains: filters.search, mode: "insensitive" } },
        { description: { contains: filters.search, mode: "insensitive" } }
      ];
    }

    if (filters?.categoryId) {
      where.categoryId = filters.categoryId;
    }

    if (filters?.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    if (filters?.isFeatured !== undefined) {
      where.isFeatured = filters.isFeatured;
    }

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        reviews: {
          select: {
            id: true,
            rating: true
          }
        },
        _count: {
          select: {
            reviews: true
          }
        }
      },
      orderBy: { createdAt: "desc" }
    });

    return { success: true, data: products };
  } catch (error) {
    console.error("Error fetching products:", error);
    return { success: false, error: "Failed to fetch products" };
  }
}

/**
 * Get a single product by ID
 */
export async function getProductById(productId: string) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        category: true,
        reviews: {
          orderBy: { createdAt: "desc" }
        },
        _count: {
          select: {
            reviews: true
          }
        }
      }
    });

    if (!product) {
      return { success: false, error: "Product not found" };
    }

    return { success: true, data: product };
  } catch (error) {
    console.error("Error fetching product:", error);
    return { success: false, error: "Failed to fetch product" };
  }
}

/**
 * Create a new product
 */
export async function createProduct(productData: {
  name: string;
  description: string;
  price: number;
  discountedPrice?: number;
  images: string[];
  brand: string;
  sizes: string[];
  colors: string[];
  stock: number;
  categoryId: string;
  isActive?: boolean;
  isFeatured?: boolean;
}) {
  try {
    const product = await prisma.product.create({
      data: {
        ...productData,
        isActive: productData.isActive ?? true
      },
      include: {
        category: true,
        _count: {
          select: {
            reviews: true
          }
        }
      }
    });

    return { success: true, data: product };
  } catch (error) {
    console.error("Error creating product:", error);
    return { success: false, error: "Failed to create product" };
  }
}

/**
 * Update a product
 */
export async function updateProduct(productId: string, productData: {
  name?: string;
  description?: string;
  price?: number;
  discountedPrice?: number;
  images?: string[];
  brand?: string;
  sizes?: string[];
  colors?: string[];
  stock?: number;
  categoryId?: string;
  isActive?: boolean;
  isFeatured?: boolean;
}) {
  try {
    const product = await prisma.product.update({
      where: { id: productId },
      data: {
        ...productData,
        updatedAt: new Date()
      },
      include: {
        category: true,
        _count: {
          select: {
            reviews: true
          }
        }
      }
    });

    return { success: true, data: product };
  } catch (error) {
    console.error("Error updating product:", error);
    return { success: false, error: "Failed to update product" };
  }
}

/**
 * Delete a product
 */
export async function deleteProduct(productId: string) {
  try {
    // First check if product exists and has any orders
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        _count: {
          select: {
            orderItems: true
          }
        }
      }
    });

    if (!product) {
      return { success: false, error: "Product not found" };
    }

    if (product._count.orderItems > 0) {
      // If product has orders, just mark as inactive instead of deleting
      const updatedProduct = await prisma.product.update({
        where: { id: productId },
        data: { isActive: false }
      });
      return { success: true, data: updatedProduct, message: "Product marked as inactive due to existing orders" };
    }

    // Delete the product if no orders exist
    await prisma.product.delete({
      where: { id: productId }
    });

    return { success: true, message: "Product deleted successfully" };
  } catch (error) {
    console.error("Error deleting product:", error);
    return { success: false, error: "Failed to delete product" };
  }
}

/**
 * Get featured products
 */
export async function getFeaturedProducts(limit: number = 8) {
  try {
    const products = await prisma.product.findMany({
      where: {
        isActive: true
      },
      include: {
        category: true,
        _count: {
          select: {
            reviews: true
          }
        }
      },
      take: limit,
      orderBy: [
        { rating: "desc" },
        { reviewCount: "desc" },
        { createdAt: "desc" }
      ]
    });

    return { success: true, data: products };
  } catch (error) {
    console.error("Error fetching featured products:", error);
    return { success: false, error: "Failed to fetch featured products" };
  }
}

/**
 * Get products by category
 */
export async function getProductsByCategory(categoryId: string, limit?: number) {
  try {
    const products = await prisma.product.findMany({
      where: {
        categoryId,
        isActive: true
      },
      include: {
        category: true,
        _count: {
          select: {
            reviews: true
          }
        }
      },
      take: limit,
      orderBy: { createdAt: "desc" }
    });

    return { success: true, data: products };
  } catch (error) {
    console.error("Error fetching products by category:", error);
    return { success: false, error: "Failed to fetch products by category" };
  }
}

/**
 * Update product stock
 */
export async function updateProductStock(productId: string, quantity: number) {
  try {
    const product = await prisma.product.update({
      where: { id: productId },
      data: {
        stock: {
          decrement: quantity
        }
      }
    });

    return { success: true, data: product };
  } catch (error) {
    console.error("Error updating product stock:", error);
    return { success: false, error: "Failed to update product stock" };
  }
}
