"use server";

import prisma from "@/lib/prisma";

/**
 * Get all categories with product counts
 */
export async function getCategories(filters?: {
  search?: string;
  isActive?: boolean;
}) {
  try {
    const where: any = {};

    if (filters?.search) {
      where.OR = [
        { name: { contains: filters.search, mode: "insensitive" } },
        { description: { contains: filters.search, mode: "insensitive" } }
      ];
    }

    if (filters?.isActive !== undefined) {
      where.isActive = filters.isActive;
    }

    const categories = await prisma.category.findMany({
      where,
      include: {
        _count: {
          select: {
            products: true
          }
        }
      },
      orderBy: { createdAt: "desc" }
    });

    return { success: true, data: categories };
  } catch (error) {
    console.error("Error fetching categories:", error);
    return { success: false, error: "Failed to fetch categories" };
  }
}

/**
 * Get a single category by ID
 */
export async function getCategoryById(categoryId: string) {
  try {
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
      include: {
        products: {
          where: { isActive: true },
          include: {
            _count: {
              select: {
                reviews: true
              }
            }
          }
        },
        _count: {
          select: {
            products: true
          }
        }
      }
    });

    if (!category) {
      return { success: false, error: "Category not found" };
    }

    return { success: true, data: category };
  } catch (error) {
    console.error("Error fetching category:", error);
    return { success: false, error: "Failed to fetch category" };
  }
}

/**
 * Create a new category
 */
export async function createCategory(categoryData: {
  name: string;
  description: string;
  image?: string;
  isActive?: boolean;
}) {
  try {
    // Check if category name already exists
    const existingCategory = await prisma.category.findFirst({
      where: {
        name: {
          equals: categoryData.name,
          mode: "insensitive"
        }
      }
    });

    if (existingCategory) {
      return { success: false, error: "Category with this name already exists" };
    }

    const category = await prisma.category.create({
      data: {
        ...categoryData,
        isActive: categoryData.isActive ?? true
      },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    });

    return { success: true, data: category };
  } catch (error) {
    console.error("Error creating category:", error);
    return { success: false, error: "Failed to create category" };
  }
}

/**
 * Update a category
 */
export async function updateCategory(categoryId: string, categoryData: {
  name?: string;
  description?: string;
  image?: string;
  isActive?: boolean;
}) {
  try {
    // If updating name, check if it already exists
    if (categoryData.name) {
      const existingCategory = await prisma.category.findFirst({
        where: {
          name: {
            equals: categoryData.name,
            mode: "insensitive"
          },
          id: {
            not: categoryId
          }
        }
      });

      if (existingCategory) {
        return { success: false, error: "Category with this name already exists" };
      }
    }

    const category = await prisma.category.update({
      where: { id: categoryId },
      data: {
        ...categoryData,
        updatedAt: new Date()
      },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    });

    return { success: true, data: category };
  } catch (error) {
    console.error("Error updating category:", error);
    return { success: false, error: "Failed to update category" };
  }
}

/**
 * Delete a category
 */
export async function deleteCategory(categoryId: string) {
  try {
    // Check if category has products
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    });

    if (!category) {
      return { success: false, error: "Category not found" };
    }

    if (category._count.products > 0) {
      return { 
        success: false, 
        error: `Cannot delete category with ${category._count.products} products. Please move or delete the products first.` 
      };
    }

    await prisma.category.delete({
      where: { id: categoryId }
    });

    return { success: true, message: "Category deleted successfully" };
  } catch (error) {
    console.error("Error deleting category:", error);
    return { success: false, error: "Failed to delete category" };
  }
}

/**
 * Get active categories for public use
 */
export async function getActiveCategories() {
  try {
    const categories = await prisma.category.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: {
            products: {
              where: { isActive: true }
            }
          }
        }
      },
      orderBy: { name: "asc" }
    });

    return { success: true, data: categories };
  } catch (error) {
    console.error("Error fetching active categories:", error);
    return { success: false, error: "Failed to fetch active categories" };
  }
}

/**
 * Toggle category status
 */
export async function toggleCategoryStatus(categoryId: string) {
  try {
    const category = await prisma.category.findUnique({
      where: { id: categoryId }
    });

    if (!category) {
      return { success: false, error: "Category not found" };
    }

    const updatedCategory = await prisma.category.update({
      where: { id: categoryId },
      data: {
        isActive: !category.isActive,
        updatedAt: new Date()
      },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    });

    return { success: true, data: updatedCategory };
  } catch (error) {
    console.error("Error toggling category status:", error);
    return { success: false, error: "Failed to toggle category status" };
  }
}

/**
 * Get category statistics
 */
export async function getCategoryStats() {
  try {
    const [
      totalCategories,
      activeCategories,
      categoriesWithProducts,
      categoriesWithImages
    ] = await Promise.all([
      prisma.category.count(),
      prisma.category.count({ where: { isActive: true } }),
      prisma.category.count({
        where: {
          products: {
            some: {}
          }
        }
      }),
      prisma.category.count({
        where: {
          image: {
            not: null
          }
        }
      })
    ]);

    return {
      success: true,
      data: {
        totalCategories,
        activeCategories,
        categoriesWithProducts,
        categoriesWithImages
      }
    };
  } catch (error) {
    console.error("Error fetching category stats:", error);
    return { success: false, error: "Failed to fetch category statistics" };
  }
}
