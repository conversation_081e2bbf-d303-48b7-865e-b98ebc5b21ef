"use server";

import prisma from "@/lib/prisma";
import { User } from "@/utils/types";

/**
 * Create a new user
 */
export async function createUser(userData: Partial<User>) {
  try {
    const user = await prisma.user.create({
      data: userData as any,
    });
    return { success: true, data: user };
  } catch (error) {
    console.error("Error creating user:", error);
    return { success: false, error: "Failed to create user" };
  }
}

/**
 * Get a user by ID
 */
export async function getUserById(userId: string) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });
    return { success: true, data: user };
  } catch (error) {
    console.error("Error fetching user:", error);
    return { success: false, error: "Failed to fetch user" };
  }
}

/**
 * Update a user
 */
export async function updateUser(userId: string, userData: Partial<User>) {
  try {
    const user = await prisma.user.update({
      where: { id: userId },
      data: userData as any,
    });
    return { success: true, data: user };
  } catch (error) {
    console.error("Error updating user:", error);
    return { success: false, error: "Failed to update user" };
  }
}

/**
 * Delete a user
 */
export async function deleteUser(userId: string) {
  try {
    await prisma.user.delete({
      where: { id: userId },
    });
    return { success: true };
  } catch (error) {
    console.error("Error deleting user:", error);
    return { success: false, error: "Failed to delete user" };
  }
}

/**
 * Get all users
 */
export async function getAllUsers() {
  try {
    const users = await prisma.user.findMany();
    return { success: true, data: users };
  } catch (error) {
    console.error("Error fetching users:", error);
    return { success: false, error: "Failed to fetch users" };
  }
}

/**
 * Update user profile
 */
export async function updateUserProfile(userId: string, profileData: { name: string; email: string }) {
  try {
    const user = await prisma.user.update({
      where: { id: userId },
      data: {
        name: profileData.name,
        email: profileData.email,
        updatedAt: new Date(),
      },
    });
    return { success: true, data: user };
  } catch (error) {
    console.error("Error updating user profile:", error);
    return { success: false, error: "Failed to update profile" };
  }
}

/**
 * Update user role (admin only)
 */
export async function updateUserRole(userId: string, role: "USER" | "ADMIN") {
  try {
    const user = await prisma.user.update({
      where: { id: userId },
      data: {
        role,
        updatedAt: new Date(),
      },
    });
    return { success: true, data: user };
  } catch (error) {
    console.error("Error updating user role:", error);
    return { success: false, error: "Failed to update user role" };
  }
}
